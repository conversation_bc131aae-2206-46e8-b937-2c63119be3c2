const express = require("express");
const {
  getUserDomainContacts,
  createOrUpdateDomain<PERSON>ontact,
  deleteDomain<PERSON>ontact,
  copyDomainContact,
} = require("../controllers/dn-management/userContactController");
const { checkUserOrRefreshToken } = require("../midelwares/authorization");

const userContactRouter = express.Router();

// All routes require authentication
userContactRouter.use(checkUserOrRefreshToken);

// Get user's domain contacts
userContactRouter.get("/domain-contacts", getUserDomainContacts);

// Create or update a domain contact
userContactRouter.post("/domain-contacts", createOrUpdateDomainContact);

// Delete a domain contact
userContactRouter.delete("/domain-contacts/:contactType", deleteDomainContact);

// Copy contact from one type to another
userContactRouter.post("/domain-contacts/copy", copyDomainContact);

// Debug endpoint to test contact creation
userContactRouter.post("/debug-contact-creation", async (req, res) => {
  try {
    const { contactDetails } = req.body;
    const customerId = process.env.COMPANY_CUSTOMER_ID || "31174676";

    console.log("🧪 Debug: Testing contact creation");
    console.log("Contact details:", contactDetails);
    console.log("Customer ID:", customerId);

    const contactService = require("../services/contactService");

    // Test the contact service directly
    const result = await contactService.addContact(contactDetails, customerId);

    console.log("✅ Debug: Contact creation successful");
    console.log("Result:", result);

    res.json({
      success: true,
      message: "Contact creation test successful",
      result,
      customerId,
    });
  } catch (error) {
    console.error("❌ Debug: Contact creation failed");
    console.error("Error:", error.message);
    console.error("Full error:", error);

    res.status(500).json({
      success: false,
      error: error.message,
      details: error.response?.data || error.stack,
    });
  }
});

module.exports = userContactRouter;
