# Domain Registration with User Contacts - Implementation Update

## Overview

The domain registration service has been updated to use user-specific contact information stored in both MongoDB and the reseller database when registering domains after successful payment confirmation from Payzone.

## Key Architecture Changes

### 1. Contact Storage Strategy

- **Dual Storage**: Contacts are stored in both MongoDB (local) and reseller database (external)
- **Contact ID Mapping**: External contact IDs from reseller API are stored in MongoDB
- **Sync on Updates**: When users edit contacts, both databases are updated
- **Company Customer ID**: All registrations use the same company customer ID from environment variables

## Changes Made

### 1. Updated Domain Registration Service (`backend/services/domainRegistrationService.js`)

#### Key Changes:

- **Company Customer ID**: Now uses environment variable `process.env.COMPANY_CUSTOMER_ID` with fallback to "31174676"
- **User Contacts**: Retrieves and uses user's stored domain contacts from MongoDB
- **External Contact IDs**: Uses the external contact IDs stored from reseller API responses
- **Updated Nameservers**: Uses the new nameserver configuration:
  - `moha1280036.mercury.orderbox-dns.com`
  - `moha1280036.venus.orderbox-dns.com`
  - `moha1280036.earth.orderbox-dns.com`
  - `moha1280036.mars.orderbox-dns.com`

#### New Functions:

- `getUserDomainContacts(userId)`: Fetches user's domain contacts from database
- Enhanced error handling for missing contacts
- Improved logging for debugging

#### Registration Flow:

1. Payment confirmed successfully via Payzone webhook
2. `markOrderAsPaid` function called in `orderService.js`
3. `processDomainRegistrations` identifies domain suborders
4. For each domain suborder:
   - Fetches user's domain contacts from database
   - Uses company customer ID (31174676)
   - Uses user's contact IDs for registrant, admin, tech, and billing
   - Calls domain registration API with proper parameters
   - Updates suborder status to ACTIVE on success

### 2. Contact Management Flow

#### Contact Creation Process:

1. **User Creates Contact**: Via frontend contact management interface
2. **API Call to Reseller**: Contact details sent to reseller database
3. **External Contact ID**: Reseller API returns unique contact ID
4. **MongoDB Storage**: Contact stored locally with external ID reference
5. **User Association**: Contact linked to user's domain contact references

#### Contact Update Process:

1. **User Updates Contact**: Via frontend interface
2. **Dual Update**: Both MongoDB and reseller database updated
3. **Sync Verification**: Ensures both databases remain consistent
4. **Fallback Handling**: Graceful handling if external API fails

### 3. Contact Requirements

For domain registration to work, users must have all four contact types configured:

- **Registrant Contact**: Required for domain ownership
- **Admin Contact**: Administrative contact for the domain
- **Tech Contact**: Technical contact for the domain
- **Billing Contact**: Billing contact for the domain

Each contact must have a valid `externalContactId` from the reseller database.

### 4. API Parameters

The registration API call now includes:

```javascript
{
  "auth-userid": process.env.AUTH_USERID_PROD,
  "api-key": process.env.API_KEY_PROD,
  "domain-name": "example.com",
  "years": 1,
  "customer-id": process.env.COMPANY_CUSTOMER_ID, // From environment
  "reg-contact-id": userContacts.registrant,      // From user's MongoDB record
  "admin-contact-id": userContacts.admin,         // From user's MongoDB record
  "tech-contact-id": userContacts.tech,           // From user's MongoDB record
  "billing-contact-id": userContacts.billing,     // From user's MongoDB record
  "invoice-option": "KeepInvoice",
  "auto-renew": false,
  "purchase-privacy": true,
  "protect-privacy": true,
  "ns": [
    "moha1280036.mercury.orderbox-dns.com",
    "moha1280036.venus.orderbox-dns.com",
    "moha1280036.earth.orderbox-dns.com",
    "moha1280036.mars.orderbox-dns.com"
  ]
}
```

### 5. Environment Variables Required

Add these to your `.env` file:

```bash
# Domain Registration API Credentials
AUTH_USERID_PROD=your_auth_userid
API_KEY_PROD=your_api_key

# Company Customer ID for all domain registrations
COMPANY_CUSTOMER_ID=31174676
```

## Testing Instructions

### 1. Prerequisites

- Ensure users have domain contacts configured via the contact management system
- Verify environment variables are set:
  - `AUTH_USERID_PROD`
  - `API_KEY_PROD`

### 2. Test Scenario

1. **Create User Contacts**: Use the contact management endpoints to create all required contact types for a test user
2. **Add Domain to Cart**: Add a domain name to the user's cart
3. **Place Order**: Create an order with the domain item
4. **Process Payment**: Use Payzone to process payment successfully
5. **Verify Registration**: Check logs for domain registration attempts and verify suborder status updates

### 3. API Endpoints for Contact Management

```bash
# Get user's domain contacts
GET /api/user-contacts/domain-contacts

# Create/update domain contact
POST /api/user-contacts/domain-contacts
{
  "contactType": "registrant|admin|tech|billing",
  "contactDetails": {
    "name": "John Doe",
    "email": "<EMAIL>",
    "company": "Test Company",
    "address": "123 Main St",
    "city": "Test City",
    "country": "US",
    "zipcode": "12345",
    "phoneCountryCode": "1",
    "phone": "5551234567"
  }
}
```

### 4. Testing with Postman

Use the provided test script (`backend/test-domain-registration.js`) to verify the logic:

```bash
cd backend
node test-domain-registration.js
```

## Error Handling

### Missing Contacts Error

If a user doesn't have all required contacts configured, the registration will fail with:

```
Missing required contacts: admin, billing. Please ensure all contact types are configured for the user.
```

### API Failure Handling

- Registration failures are logged but don't stop order processing
- Failed registrations return detailed error information
- Suborder status remains in PROCESSING state for failed registrations

## Monitoring and Debugging

### Log Messages to Monitor

- `🔄 Processing domain registrations for order: [orderId]`
- `🔄 Fetching user domain contacts for user: [userId]`
- `🔄 Registering domain: [domainName]`
- `✅ Domain registration successful: [response]`
- `❌ Domain registration error: [error]`

### Database Changes

- Suborders with successful domain registrations will have `status: "ACTIVE"`
- Failed registrations will remain in `status: "PROCESSING"`

## Security Considerations

- Company customer ID is hardcoded for security
- User contact IDs are validated before use
- API credentials are stored in environment variables
- All API calls include proper authentication

## Next Steps

1. **Test the Implementation**: Use the testing instructions above
2. **Monitor Logs**: Watch for successful domain registrations in production
3. **User Experience**: Ensure users can easily manage their domain contacts
4. **Error Handling**: Monitor for any edge cases or API failures

## Files Modified

- `backend/services/domainRegistrationService.js` - Main implementation
- `backend/docs/domain-registration-user-contacts-update.md` - This documentation
- `backend/test-domain-registration.js` - Test script

## Contact Information

For questions or issues with this implementation, refer to the domain management controller and contact management system documentation.
